*** Settings ***
Library    ${CURDIR}/../../../Libs/Settings/get_data_sheet.py    WITH NAME    DataSheet

*** Keywords ***
Get Account Credentials
    [Arguments]    ${role}
    ${username}=    DataSheet.get_variable    ${role}_USER
    ${password}=    DataSheet.get_variable    ${role}_PASS
    RETURN    ${username}    ${password}

Load Dynamic Variables
    [Documentation]    Load variables from Google Sheets
    ${ADMIN_USER}=      DataSheet.get_variable    ADMIN_USER
    ${ADMIN_PASS}=      DataSheet.get_variable    ADMIN_PASS
    ${DEAN_USER}=       DataSheet.get_variable    DEAN_USER
    ${DEAN_PASS}=       DataSheet.get_variable    DEAN_PASS
    ${TEACHER_USER}=    DataSheet.get_variable    TEACHER_USER
    ${TEACHER_PASS}=    DataSheet.get_variable    TEACHER_PASS
    ${STUDENT_USER}=    DataSheet.get_variable    STUDENT_USER
    ${STUDENT_PASS}=    DataSheet.get_variable    STUDENT_PASS

    Set Global Variable    ${ADMIN_USER}
    Set Global Variable    ${ADMIN_PASS}
    Set Global Variable    ${DEAN_USER}
    Set Global Variable    ${DEAN_PASS}
    Set Global Variable    ${TEACHER_USER}
    Set Global Variable    ${TEACHER_PASS}
    Set Global Variable    ${STUDENT_USER}
    Set Global Variable    ${STUDENT_PASS}
    