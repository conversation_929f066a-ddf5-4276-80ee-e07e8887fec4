# Robot Framework output files
output.xml
log.html
report.html
*.png
*.jpg
*.jpeg
*.gif
selenium-screenshot-*.png
robot-*.log

# Test results and logs
results/
logs/
screenshots/
*.log

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Selenium WebDriver
chromedriver
geckodriver
edgedriver
operadriver
*.exe

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Configuration files with sensitive data
config.ini
secrets.yaml
.env
.env.local
.env.*.local
service-account-sheets.json

# Node.js (if using any JS tools)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# Backup files
*.bak
*.backup
*~

# Local test data
test-data/
local-test-files/

# Browser profiles
browser-profiles/
