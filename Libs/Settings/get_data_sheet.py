import gspread
import os
from os.path import join, dirname
from dotenv import load_dotenv
import json

class SheetDataManager:
    """
        Manage data from Google Sheets
        Supports both Python (snake_case) and Robot Framework (UPPER_CASE)
    """
    
    def __init__(self):
        # Connect Google Sheets
        load_dotenv()
        self.service_account_file = join(join(dirname(dirname(__file__)), 'Settings'), "service-account-sheets.json")
        self.sheet_key = os.environ.get("SHEET_KEY")

        # Initialize with default values in case of connection issues
        self.email_config = {}
        self.account_data = {}
        self.ROBOT_VARIABLES = {}

        try:
            self.gs = gspread.service_account(self.service_account_file)
            self.sheet = self.gs.open_by_key(self.sheet_key)
            self.worksheet = self.sheet.sheet1

            print(f"📊 Sheet: {self.sheet.title}")

            # Read data from sheet
            self._load_data()
        except Exception as e:
            print(f"⚠️ Warning: Could not connect to Google Sheets: {e}")
            print("📝 Using default/fallback values")
            self._load_default_data()
        
    def _load_data(self):
        """
            Read data from Google Sheets and initialize properties
            Get JSON data from cell B3 (EMAIL_CONFIG)
            Get JSON data from cell B4 (ACCOUNT)
        """
        email_config_json = self.worksheet.acell('B3').value
        self.email_config = json.loads(email_config_json)
        
        account_json = self.worksheet.acell('B4').value
        self.account_data = json.loads(account_json)
        
        self._init_python_variables()
        self._init_robot_variables()

    def _load_default_data(self):
        """Load default/fallback data when Google Sheets is not available"""
        self._init_python_variables()
        self._init_robot_variables()

    def _init_python_variables(self):
        # ADMIN ACCOUNT
        self.admin_username = self.account_data['admin']['username']
        self.admin_password = self.account_data['admin']['password']
        
        # DEAN ACCOUNT
        self.dean_username = self.account_data['dean']['username']
        self.dean_password = self.account_data['dean']['password']
        
        # TEACHER ACCOUNT
        self.teacher_username = self.account_data['teacher']['username']
        self.teacher_password = self.account_data['teacher']['password']
        
        # STUDENT ACCOUNT
        self.student_username = self.account_data['student']['username']
        self.student_password = self.account_data['student']['password']
    
    def _init_robot_variables(self):
        """Initialize variables according to Robot Framework convention (UPPER_CASE)"""
        self.ROBOT_VARIABLES = {
            'ADMIN_USER': self.admin_username,
            'ADMIN_PASS': self.admin_password,
            'DEAN_USER': self.dean_username,
            'DEAN_PASS': self.dean_password,
            'TEACHER_USER': self.teacher_username,
            'TEACHER_PASS': self.teacher_password,
            'STUDENT_USER': self.student_username,
            'STUDENT_PASS': self.student_password,
        }
    
    def get_robot_variable(self, name):
        """Get variable value for Robot Framework"""
        return self.ROBOT_VARIABLES.get(name, None)
    
    def get_email_config(self):
        """Return email configuration as dict"""
        return self.email_config

# Create default instance for use in other modules
data_manager = SheetDataManager()


# This function will be called from Robot Framework
def get_variable(name):
    return data_manager.get_robot_variable(name)

print(get_variable("TEACHER_USER"))
print(get_variable("TEACHER_PASS"))
print(data_manager.get_email_config())

