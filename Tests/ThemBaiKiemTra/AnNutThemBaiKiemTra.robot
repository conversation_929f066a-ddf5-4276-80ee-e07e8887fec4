*** Variables ***
${LECTURER_USER1}     lecturer1
${LECTURER_PASS1}     demolecturer

${A_HOC_PHAN}              xpath=//a[@href="/en/academics/courses/" and contains(@class, "menu-item")]
${A_DETAI_COURSE}          xpath=//a[@href="/en/academics/courses/1/"]
${A_THEM_BAI_KIEM_TRA}     xpath=//a[@href="/en/academics/courses/1/assessments/create/"]


*** Settings ***
Library    SeleniumLibrary
Resource    ../../Resources/PageObject/KeywordDefinationFiles/LoginPage.robot
Resource    ../../Resources/PageObject/Locators/HomeLocators.robot
Resource    ../../Resources/PageObject/TestData/Accounts.robot

*** Test Cases ***
CLICK THEM BAI KIEM TRA
    Open Browser    ${URL}    ${BROWSER}

    # Login
    Login With Credentials    ${LECTURER_USER}    ${LECTURER_PASS}
#    Sleep    200s
    # Click vào Học phần
    Wait Until Element Is Visible    ${A_HOC_PHAN}    10s
    Click Element    ${A_HOC_PHAN}
    Sleep    2s
#    Sleep    200s
    Wait Until Element Is Visible    ${A_DETAI_COURSE}    10s
    Click Element    ${A_DETAI_COURSE}
    Sleep    2s

    Wait Until Element Is Visible    ${A_THEM_BAI_KIEM_TRA}    10s
    # Click vào Thêm bài kiểm tra
    Click Element    ${A_THEM_BAI_KIEM_TRA}
    Sleep    2s

    Sleep    20s

    # Exe logout
    Logout Normal
    [Teardown]    Close Browser
